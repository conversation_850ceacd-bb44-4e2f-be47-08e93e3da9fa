import {
  HttpException,
  Injectable,
  NotFoundException,
  Logger,
  HttpStatus,
} from '@nestjs/common';
import {
  CreatePatientDetailsDto,
  CreateQuickEncounterPatientDto,
} from './dtos/CreatePatientDetails.dto';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { Gender, Patient } from '@prisma/client';
import { PaginatedResultDTO, PaginationResDTO } from 'src/common/dtos';
import { UtilsService } from 'src/common/utils/utils.service';
import { s3FileUploadService } from 'src/common/s3-file-management/s3FileUpload.service';


@Injectable()
export class PatientRepository {
  private readonly logger = new Logger(PatientRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
    private readonly s3FileUploadService: s3FileUploadService,

  ) { }

  async insertPatient(
    data: CreatePatientDetailsDto | CreateQuickEncounterPatientDto,
    userEmail: string,
  ): Promise<Patient> {
    try {
      const patient = await this.prisma.patient.create({
        data: {
          first_name: data.patientFirstName || null,
          last_name: data.patientLastName || undefined,
          date_of_birth: data.patientDOB
            ? new Date(data.patientDOB).toISOString()
            : null,
          gender: Gender[data.patientGender.toUpperCase()],
          phone_number: data.patientPhone || null,
          phone_country_code: data.phoneCountryCode || null,
          created_by: userEmail,
        },
      });

      return patient;
    } catch (error) {
      this.logger.error('Error creating patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create patient record',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPatientByPhone(
    phone: string,
    userEmail: string,
    page: number = 0,
    limit: number = 10,
    full_name: string | undefined = undefined,
    phone_or_name: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<Patient>> {
    try {
      let first_name: string = undefined;
      let last_name: string = undefined;

      if (full_name) {
        const parts = full_name.trim().split(' ');
        first_name = parts[0];
        if (parts.length > 1) last_name = parts[1];
      }

      // dynamic OR conditions using an array
      const searchConditions = [];

      if (phone) {
        searchConditions.push({
          phone_number: {
            startsWith: phone,
          },
        });
      }

      if (full_name) {
        if (full_name.trim().includes(' ')) {
          first_name = full_name.split(' ')[0];
          last_name = full_name.split(' ')[1];
        } else {
          first_name = full_name;
          last_name = full_name;
        }
      }

      if (phone_or_name) {
        searchConditions.push({
          OR: [
            {
              phone_number: {
                startsWith: phone_or_name,
              },
            },
            {
              first_name: {
                startsWith: phone_or_name,
                mode: 'insensitive',
              },
            },
            {
              last_name: {
                startsWith: phone_or_name,
                mode: 'insensitive',
              },
            },
          ],
        });
      }

      const [patient, totalRecords] = await this.prisma.$transaction([
        this.prisma.patient.findMany({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  // Phone number search
                  {
                    phone_number: phone
                      ? {
                        startsWith: phone,
                      }
                      : undefined,
                  },
                  // First name only
                  {
                    first_name:
                      !first_name && !last_name
                        ? {
                          startsWith: full_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  // Last name only
                  {
                    last_name:
                      !last_name && !first_name
                        ? {
                          startsWith: full_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  // Both first name AND last name
                  {
                    // AND: [
                    //   {
                    //     first_name:
                    //       first_name && last_name
                    //         ? {
                    //             startsWith: first_name,
                    //             mode: 'insensitive',
                    //           }
                    //         : undefined,
                    //   },
                    //   {
                    //     last_name:
                    //       last_name && first_name
                    //         ? {
                    //             startsWith: last_name,
                    //             mode: 'insensitive',
                    //           }
                    //         : undefined,
                    //   },
                    // ],
                  },
                  {
                    first_name:
                      first_name && last_name
                        ? {
                          contains: first_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  {
                    last_name:
                      last_name && first_name
                        ? {
                          contains: last_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                ],
              },
              {
                OR: [
                  {
                    created_by: userEmail,
                  },
                  {
                    created_by: 'ADMIN',
                  },
                ],
              },
            ],
          },
          skip: (page - 1) * limit,
          take: limit,
        }),

        this.prisma.patient.count({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  // Phone number search
                  {
                    phone_number: phone
                      ? {
                        startsWith: phone,
                      }
                      : undefined,
                  },
                  // First name only
                  {
                    first_name:
                      !first_name && !last_name
                        ? {
                          startsWith: full_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  // Last name only
                  {
                    last_name:
                      !last_name && !first_name
                        ? {
                          startsWith: full_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  // Both first name AND last name
                  {
                    // AND: [
                    //   {
                    //     first_name:
                    //       first_name && last_name
                    //         ? {
                    //             startsWith: first_name,
                    //             mode: 'insensitive',
                    //           }
                    //         : undefined,
                    //   },
                    //   {
                    //     last_name:
                    //       last_name && first_name
                    //         ? {
                    //             startsWith: last_name,
                    //             mode: 'insensitive',
                    //           }
                    //         : undefined,
                    //   },
                    // ],
                  },
                  {
                    first_name:
                      first_name && last_name
                        ? {
                          contains: first_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                  {
                    last_name:
                      last_name && first_name
                        ? {
                          contains: last_name,
                          mode: 'insensitive',
                        }
                        : undefined,
                  },
                ],
              },
              {
                OR: [
                  {
                    created_by: userEmail,
                  },
                  {
                    created_by: 'ADMIN',
                  },
                ],
              },
            ],
          },
        }),
      ]);
      let response;
      if (patient.length) response = patient.filter(p => p.first_name);
      return {
        data: response,
        pagination: this.utilsService.getPagination(page, limit, totalRecords),
      };
    } catch (error) {
      this.logger.error('Error retrieving patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  async updatePatient(
    model: CreatePatientDetailsDto,
    userEmail: string,
    patient_id: string,
    patientImage?: Express.Multer.File,
  ): Promise<Patient> {
    try {
      const existingPatient = await this.prisma.patient.findFirst({
        where: { patient_id, isActive: true },
      });

      if (!existingPatient) {
        this.logger.error(`Patient not found for patient_id: ${patient_id}`);
        throw new NotFoundException('Patient not found');
      }

      let imageUrl: string | undefined;

      // 🔹 If new image provided, handle S3 upload + delete old
      if (patientImage) {
        // If there’s already an image, delete it
        if (existingPatient.patient_image) {
          await this.s3FileUploadService.deleteFileFromS3(existingPatient.patient_image);
        }

        // Upload new file to S3
        const filePath = `patients/${patient_id}/${Date.now()}-${patientImage.originalname}`;
        imageUrl = await this.s3FileUploadService.uploadFileToS3(patientImage, filePath);
      }

      // 🔹 Update DB (single update call)
      const updatedPatient = await this.prisma.patient.update({
        where: { patient_id },
        data: {
          first_name: model.patientFirstName,
          last_name: model.patientLastName ?? undefined,
          date_of_birth: new Date(model.patientDOB).toISOString(),
          gender: model.patientGender,
          phone_number: model.patientPhone,
          phone_country_code: model.phoneCountryCode || null,
          ...(imageUrl && { patient_image: imageUrl }), // only update if new image
          updated_by: userEmail,
          updated_at: new Date().toISOString(),
          address: model.address,
          country: model.country,
          state: model.state,
          pin: model.pin,
          district_name: model.districtName
        },
      });

      return updatedPatient;
    } catch (error) {
      this.logger.error('Error updating Patient:', error?.stack);

      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }

      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  async deletePatient(patient_id: string, userEmail: string): Promise<Patient> {
    try {
      const existingPatient = await this.prisma.patient.count({
        where: { patient_id: patient_id, isActive: true },
      });
      if (existingPatient === 0) {
        this.logger.error(
          `Patient not found for the given patient ID: ${patient_id}`,
        );
        throw new NotFoundException('Patient not found');
      }

      const patient = await this.prisma.patient.update({
        where: { patient_id: patient_id, isActive: true },
        data: {
          isActive: false,
          updated_by: userEmail,
        },
      });
      return patient;
    } catch (error) {
      this.logger.error('Error deleting Patient:', error?.stack);
      // If it's already a NotFoundException, keep the original status code
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllPatient(
    page: number,
    limit: number,
    userEmail: string,
  ): Promise<PaginatedResultDTO<Patient>> {
    try {
      const [patient, totalRecords] = await this.prisma.$transaction([
        this.prisma.patient.findMany({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  {
                    created_by: 'ADMIN',
                  },
                  {
                    created_by: userEmail,
                  },
                ],
              },
            ],
          },
          orderBy: [{ updated_at: 'desc' }, { created_at: 'desc' }],
          skip: (page - 1) * limit,
          take: limit,
        }),

        this.prisma.patient.count({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  {
                    created_by: 'ADMIN',
                  },
                  {
                    created_by: userEmail,
                  },
                ],
              },
            ],
          },
        }),
      ]);
      const response = patient?.filter(p => p.first_name);
      return {
        data: response,
        pagination: this.utilsService.getPagination(page, limit, totalRecords),
      };
    } catch (error) {
      this.logger.error('Error retrieving patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPatientById(
    patient_id: string,
    userEmail: string,
  ): Promise<Patient> {
    try {
      const patient = await this.prisma.patient.findUnique({
        where: {
          patient_id: patient_id,
          isActive: true,
          AND: [
            {
              OR: [
                {
                  created_by: 'ADMIN',
                },
                {
                  created_by: userEmail,
                },
              ],
            },
          ],
        },
        include: {
          encounters: {
            include: {
              labs: true,
              Prescriptions: true,
              tbl_vital_measurements: {
                include: {
                  tbl_vital_types: true,
                },
              },
              NoteHeader: {
                select: {
                  note_type: {
                    select: {
                      note_type_id: true,
                      note_type_name: true,
                    },
                  },
                  note_details: {
                    select: {
                      note_section: {
                        select: {
                          note_section_id: true,
                          section_name: true,
                        },
                      },
                      value: true,
                    },
                  },
                },
              },
            },
          },
        },
      });
      delete patient.external_id_1;
      delete patient.external_id_2;
      return patient;
    } catch (error) {
      this.logger.error('Error retrieving patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getVitalsByPatientId(
    patient_id: string,
    userEmail: string,
  ): Promise<Patient> {
    try {
      const twoYearsAgo = new Date();
      twoYearsAgo.setFullYear(twoYearsAgo.getFullYear() - 2);
      const patient = await this.prisma.patient.findUnique({
        where: {
          patient_id: patient_id,
          isActive: true,
          AND: [
            {
              OR: [
                {
                  created_by: 'ADMIN',
                },
                {
                  created_by: userEmail,
                },
              ],
            },
          ],
        },
        include: {
          encounters: {
            where: {
              encounter_date: {
                gte: twoYearsAgo, // only last 2 years encounters
              }
            },
            include: {
              tbl_vital_measurements: {
                include: {
                  tbl_vital_types: true,
                },
              },
            },
          },
        },
      });
      return patient;
    } catch (error) {
      this.logger.error('Error retrieving patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  async getEncountersByPatientId(patient_id: string, userEmail: string): Promise<any[]> {
    try {
      return await this.prisma.encounter.findMany({
        where: { patient_id, isActive: true },
        include: {
          doctor: true,
          encounterstatus: true,
          note_type: true,
          labs: true,
          Prescriptions: true,
          tbl_vital_measurements: {
            include: { tbl_vital_types: true },
          },
          NoteHeader: {
            select: {
              note_type: { select: { note_type_id: true, note_type_name: true } },
              note_details: {
                select: {
                  note_section: { select: { note_section_id: true, section_name: true } },
                  value: true,
                },
              },
            },
          },
        },
        orderBy: { encounter_date: 'desc' },
      });
    } catch (error) {
      this.logger.error('Error retrieving encounters:', error?.stack);
      throw this.utilsService.formatErrorResponse(error, 'Failed to retrieve encounters', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }
}
