import {
  BadRequestException,
  HttpStatus,
  Injectable,
  Logger,
} from '@nestjs/common';
import { AudioTranscription, TranscriptStatus } from '@prisma/client';
import { bool } from 'aws-sdk/clients/signer';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class TranscribeRepository {
  private readonly logger = new Logger(TranscribeRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async createAudioTranscription(file: {
    originalname?: string;
    mimetype?: string;
    s3Uri?: string;
    buffer?: Buffer;
    path?: string;
    size?: number;
    patient_id?: string;
    encounter_id?: string;
    user_email?: string;
    duration?: string;
    type?: string;
    status?: TranscriptStatus;
  }) {
    try {
      const typeExists = await this.prisma.transcriptionType.count({
        where: {
          transcription_type: {
            equals: file.type,
            mode: 'insensitive',
          },
          isActive: true,
        },
      });

      if (typeExists === 0) {
        throw new BadRequestException('type does not exist');
      }

      const audioRecord = await this.prisma.audioTranscription.create({
        data: {
          original_filename: file.originalname || 'unknown.wav',
          s3_uri: file.s3Uri || '',
          file_size: BigInt(file.size || 0),
          mime_type: file.mimetype || 'audio/wav',
          patient_id: file.patient_id,
          encounter_id: file.encounter_id,
          created_by: file.user_email,
          is_processed: false,
          duration: file.duration,
          transcription_type: file.type,
          status: file.status ?? undefined,
        },
      });
      return audioRecord;
    } catch (error) {
      this.logger.error('Failed to create audio Transcription:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create audio transcription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  async updateAudioTranscription(
    id: any,
    transcription: any,
    detectedLanguage: any,
    userEmail: string,
    raw_transcription: any,
    duration?: string,
    status?: TranscriptStatus,
  ) {
    let updateData: any = {
      transcription,
      detected_language: detectedLanguage,
      is_processed: true,
      updated_by: userEmail,
      raw_transcription: raw_transcription,
      status: status ?? undefined,
    };

    if (duration) {
      updateData.duration = duration;
    }

    try {
      const updatedAudioRecord = await this.prisma.audioTranscription.update({
        where: { id: id },
        data: updateData,
      });
      return updatedAudioRecord;
    } catch (error) {
      this.logger.error('Failed to create audio Transcription:', error.message);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update audio transcription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getTrancriptionTypeCount(
    encounterId: string,
    transcription_type: string,
  ): Promise<number> {
    try {
      const transcriptionTypeExists = await this.prisma.transcriptionType.count(
        {
          where: {
            transcription_type: {
              equals: transcription_type,
              mode: 'insensitive',
            },
            isActive: true,
          },
        },
      );

      if (transcriptionTypeExists === 0) {
        throw new BadRequestException('type does not exist');
      }
      const count = await this.prisma.audioTranscription.count({
        where: {
          encounter_id: encounterId,
          transcription_type: {
            equals: transcription_type,
            mode: 'insensitive',
          },
          is_processed: true,
        },
      });
      return count;
    } catch (error) {
      this.logger.error(
        'Failed to get transcription type count:',
        error?.stack,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to get transcription type count',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async crateAudioTranscriptionLive(model: {
    original_filename: string;
    encounter_id: string;
    created_by: string;
    is_processed: boolean;
    duration: string;
    transcription_type: string;
    s3_uri: string;
    file_size: number;
    mime_type: string;
    raw_transcription: string;
    transcription: string;
    detected_language: string;
    patient_id?: string;
  }) {
    try {
      const audioRecord = await this.prisma.audioTranscription.create({
        data: {
          original_filename: model.original_filename,
          s3_uri: model.s3_uri,
          file_size: BigInt(model.file_size || 0),
          mime_type: model.mime_type,
          patient_id: model.patient_id,
          encounter_id: model.encounter_id,
          created_by: model.created_by,
          is_processed: model.is_processed,
          duration: model.duration,
          transcription_type: model.transcription_type,
          raw_transcription: model.raw_transcription,
          transcription: model.transcription,
          detected_language: model.detected_language,
        },
      });
      return audioRecord;
    } catch (error) {
      console.log('Falied to create audio Transcription: ', error.message);
      throw error;
    }
  }
}
