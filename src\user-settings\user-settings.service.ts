import { BadRequestException, Injectable } from '@nestjs/common';
import { UserSettingsRepository } from './user-settings.repository';
import { SettingTypes, tbl_user_setting } from '@prisma/client';
import { UtilsService } from 'src/common/utils/utils.service';
import { SettingsRepository } from 'src/settings/settings.repository';
import { JsonObject, JsonValue } from '@prisma/client/runtime/library';

interface ValidationResult {
  success_validation: boolean;
  message: string;
}

@Injectable()
export class UserSettingsService {
  constructor(
    private readonly userSettingsRepository: UserSettingsRepository,
    private readonly orgSettingsRepository: SettingsRepository,
    private readonly utilsService: UtilsService,
  ) {}

  async getUserSetting(
    userEmail: string,
    settingType: SettingTypes = null,
  ): Promise<any> {
    let existingUserSetting: any =
      await this.userSettingsRepository.getUserSetting(userEmail, settingType);

    if (existingUserSetting) {
      delete existingUserSetting.user_setting_id;
      delete existingUserSetting.created_at;
      delete existingUserSetting.created_by;
      delete existingUserSetting.updated_at;
      delete existingUserSetting.updated_by;
      return existingUserSetting;
    }
    let orgActualSetting: any =
      await this.orgSettingsRepository.getSettingByConfigType(settingType);

    orgActualSetting = orgActualSetting.map(
      ({
        org_setting_id,
        created_at,
        created_by,
        updated_at,
        updated_by,
        ...rest
      }) => rest,
    );

    let filteredSetting = {
      user_id: null,
      user_email: userEmail,
      setting_type: settingType,
      description: null,
      setting_value: { setting: orgActualSetting },
    };

    return filteredSetting;
  }

  async createUserSetting(
    userEmail: string,
    settingType: SettingTypes,
    settingValue: JsonObject,
  ) {
    const existingUserSetting =
      await this.userSettingsRepository.getUserSetting(userEmail, settingType);

    let orgActualSetting: any =
      await this.orgSettingsRepository.getSettingByConfigType(settingType);

    orgActualSetting = orgActualSetting.map(
      ({
        org_setting_id,
        created_at,
        created_by,
        updated_at,
        updated_by,
        ...rest
      }) => rest,
    );

    const validationResult = this.validateSettingJson(
      { setting: orgActualSetting },
      settingValue,
    );

    if (!validationResult.success_validation) {
      throw new BadRequestException(validationResult.message);
    }

    if (existingUserSetting) {
      return await this.userSettingsRepository.updateUserSetting(
        userEmail,
        existingUserSetting.user_setting_id,
        settingType,
        settingValue,
      );
    }

    return await this.userSettingsRepository.createUserSetting(
      userEmail,
      settingType,
      settingValue,
    );
  }

  async resetUserSetting(userEmail: string, settingType: SettingTypes) {
    const existingUserSetting =
      await this.userSettingsRepository.getUserSetting(userEmail, settingType);
    if (!existingUserSetting) {
      return true;
    }

    const result = await this.userSettingsRepository.deleteUserSetting(
      userEmail,
      existingUserSetting.user_setting_id,
      settingType,
    );

    if (result) {
      return true;
    }

    return false;
  }

  validateSettingJson(dbJson: any, modifiedJson: any): ValidationResult {
    const dbSettings = dbJson.setting;
    const modifiedSettings = modifiedJson.setting;

    if (dbSettings.length !== modifiedSettings.length) {
      return {
        success_validation: false,
        message: `Mismatch in number of items: expected ${dbSettings.length}, got ${modifiedSettings.length}`,
      };
    }
    const modifiedMap = new Map(
      modifiedSettings.map((item) => [item.key, item]),
    );

    for (let i = 0; i < dbSettings.length; i++) {
      const dbItem = dbSettings[i];
      const modifiedItem: any = modifiedMap.get(dbItem.key);

      if (!modifiedItem) {
        return {
          success_validation: false,
          message: `Item ${i}: Missing key '${dbItem.key}' in modified JSON`,
        };
      }

      // Check keys
      const dbKeys = Object.keys(dbItem);
      const modifiedKeys = Object.keys(modifiedItem);

      // Missing keys
      for (const key of dbKeys) {
        if (!(key in modifiedItem)) {
          return {
            success_validation: false,
            message: `Item ${i}: Missing key '${key}' in modified JSON`,
          };
        }
      }

      // Extra keys
      for (const key of modifiedKeys) {
        if (!(key in dbItem)) {
          return {
            success_validation: false,
            message: `Item ${i}: Unexpected key '${key}' in modified JSON`,
          };
        }
      }
    }

    return {
      success_validation: true,
      message: 'Validation successful ✅',
    };
  }
}
